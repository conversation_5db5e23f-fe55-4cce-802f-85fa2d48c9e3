// test-llm-config.cjs
const fs = require('fs');
const path = require('path');

// 模拟SvelteKit环境中的configManager
// 由于我们无法直接在Node.js环境中运行SvelteKit的TypeScript代码，
// 我们将创建一个简化的版本来测试配置读取功能

console.log('开始测试LLM配置功能...');

// 读取默认配置文件
const configPath = path.join(
  __dirname,
  'src',
  'lib',
  'config',
  'default-config.json',
);

try {
  // 检查配置文件是否存在
  if (!fs.existsSync(configPath)) {
    console.error('错误: 找不到配置文件', configPath);
    process.exit(1);
  }

  // 读取配置文件
  const configData = fs.readFileSync(configPath, 'utf8');
  const config = JSON.parse(configData);

  console.log('成功读取LLM配置:');
  console.log(JSON.stringify(config, null, 2));

  // 验证配置结构
  if (!config.language_models) {
    console.error('错误: 配置文件缺少language_models字段');
    process.exit(1);
  }

  // 输出所有可用的模型
  console.log('\n可用的LLM模型:');
  for (const [provider, providerConfig] of Object.entries(
    config.language_models,
  )) {
    console.log(`\n${provider}:`);
    if (providerConfig.available_models) {
      providerConfig.available_models.forEach(model => {
        console.log(`  - ${model.name} (${model.display_name})`);
      });
    }
  }

  console.log('\n测试完成!');
} catch (error) {
  console.error('测试过程中发生错误:', error.message);
  process.exit(1);
}
