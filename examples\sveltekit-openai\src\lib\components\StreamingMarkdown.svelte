<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import * as smd from 'streaming-markdown';
  import hljs from 'highlight.js';
  // Add copy buttons to code blocks after rendering
  function addCopyButtons() {
    if (!containerElement) return;
    
    const pres = containerElement.querySelectorAll('pre');
    pres.forEach(pre => {
      // avoid duplicate buttons
      if (pre.parentElement?.classList.contains('code-copy-container')) return;
      
      const code = pre.querySelector('code')?.innerText || '';
      if (!code) return;

      // Create container
      const container = document.createElement('div');
      container.className = 'code-copy-container relative';
      
      // Create copy button
      const button = document.createElement('button');
      button.type = 'button';
      button.className = 'absolute top-2 right-2 h-7 w-7 p-0 bg-background/80 backdrop-blur-sm hover:bg-background/90 border border-border/50 shadow-sm rounded-md opacity-0 hover:opacity-100 focus:opacity-100 transition-opacity duration-200 flex items-center justify-center';
      button.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground">
          <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
          <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
        </svg>
      `;
      
      let copied = false;
      button.onclick = async () => {
        try {
          await navigator.clipboard.writeText(code);
          copied = true;
          button.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
              <polyline points="20 6 9 17 4 12"/>
            </svg>
          `;
          setTimeout(() => {
            copied = false;
            button.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-muted-foreground">
                <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
              </svg>
            `;
          }, 2000);
        } catch (err) {
          console.error('Failed to copy:', err);
        }
      };
      
      // Wrap pre in container
      const parent = pre.parentNode;
      if (parent) {
        parent.insertBefore(container, pre);
        container.appendChild(pre);
        container.appendChild(button);
        
        // Add hover effects
        container.addEventListener('mouseenter', () => {
        button.classList.remove('opacity-0');
        button.classList.add('opacity-100');
      });
      
      container.addEventListener('mouseleave', () => {
        if (!button.matches(':focus')) {
          button.classList.remove('opacity-100');
          button.classList.add('opacity-0');
        }
      });
      }
    });
  }

  interface Props {
    content?: string;
    animationDelay?: number;
    fadeInDuration?: number;
    isStreaming?: boolean; // 新增：指示是否正在流式传输
  }

  let {
    content = '',
    animationDelay = 20,
    fadeInDuration = 400,
    isStreaming = false
  }: Props = $props();

  let containerElement: HTMLElement | undefined = $state();
  let parser: any;
  let renderer: any;
  let previousContent = '';
  let charIndex = 0;
  let isParserInitialized = false;

  // 为新添加的文本节点添加动画
  function addAnimationToNewText() {
    // existing animation logic ...
    // after processing, add copy buttons to code blocks
    addCopyButtons();
    if (!containerElement) return;

    const walker = document.createTreeWalker(
      containerElement,
      NodeFilter.SHOW_TEXT,
      null
    );

    let node;
    while (node = walker.nextNode()) {
      const textNode = node as Text;
      const parent = textNode.parentElement;

      if (parent && !parent.classList.contains('animated')) {
        // 标记为已处理
        parent.classList.add('animated');

        // 将文本分割成字符并添加动画
        const text = textNode.textContent || '';
        const fragment = document.createDocumentFragment();

        text.split('').forEach((char) => {
          const span = document.createElement('span');
          span.className = 'fade-in-char';
          span.textContent = char === ' ' ? '\u00A0' : char;
          span.style.animationDelay = `${charIndex * animationDelay}ms`;
          span.style.animationDuration = `${fadeInDuration}ms`;
          fragment.appendChild(span);
          charIndex++;
        });

        parent.replaceChild(fragment, textNode);
      }
    }
  }

  // 处理新增内容
  function processNewContent() {
    if (!parser || !containerElement || !isParserInitialized) return;

    const newContent = content.slice(previousContent.length);
    if (!newContent) return;

    // 写入新的内容块
    smd.parser_write(parser, newContent);
    previousContent = content;

    // 为新内容添加动画
    setTimeout(() => addAnimationToNewText(), 0);
  }

  // 结束流式传输
  function endStreaming() {
    if (parser && isParserInitialized) {
      // 调用 parser_end 来结束流并刷新剩余的 markdown
      smd.parser_end(parser);

      // 为最终内容添加动画
      setTimeout(() => addAnimationToNewText(), 0);
    }
  }

  // 创建自定义渲染器，支持代码高亮
  function createCustomRenderer(container: HTMLElement) {
    const defaultRenderer = smd.default_renderer(container);

    // 保存原始方法
    const originalAddText = defaultRenderer.add_text;
    const originalEndToken = defaultRenderer.end_token;

    // 用于跟踪代码块的累积内容
    let codeBlockContent = '';
    let isInCodeBlock = false;
    let codeLanguage = '';

    // 创建自定义渲染器对象
    const customRenderer = {
      ...defaultRenderer,

      add_token: (data: any, type: any) => {
        // 检查是否开始代码块 (使用正确的常量值)
        if (type === 9 || type === 10) { // CODE_BLOCK = 9, CODE_FENCE = 10
          isInCodeBlock = true;
          codeBlockContent = '';
          codeLanguage = '';
        }

        // 调用原始方法
        defaultRenderer.add_token(data, type);
      },

      set_attr: (data: any, attrType: any, value: string) => {
        // 如果是代码块的语言属性 (LANG = 4)
        if (isInCodeBlock && attrType === 4) {
          codeLanguage = value.replace('language-', '');
        }

        // 调用原始方法
        defaultRenderer.set_attr(data, attrType, value);
      },

      add_text: (data: any, text: string) => {
        const currentNode = data.nodes[data.index];

        // 如果在代码块中，累积内容
        if (isInCodeBlock && currentNode && currentNode.tagName === 'CODE') {
          codeBlockContent += text;

          // 应用语法高亮到累积的内容
          try {
            let highlighted;
            if (codeLanguage) {
              highlighted = hljs.highlight(codeBlockContent, { language: codeLanguage });
            } else {
              highlighted = hljs.highlightAuto(codeBlockContent);
              if (highlighted.language && !codeLanguage) {
                codeLanguage = highlighted.language;
                currentNode.className = `language-${highlighted.language}`;
              }
            }
            currentNode.innerHTML = highlighted.value;
          } catch (e) {
            // 高亮失败，使用原始文本
            currentNode.textContent = codeBlockContent;
          }
          return;
        }

        // 不是代码块，使用默认处理
        originalAddText(data, text);
      },

      end_token: (data: any) => {
        const currentNode = data.nodes[data.index];

        // 如果结束的是代码块
        if (isInCodeBlock && currentNode && currentNode.tagName === 'CODE') {
          isInCodeBlock = false;
          codeBlockContent = '';
          codeLanguage = '';
        }

        // 调用原始方法
        originalEndToken(data);
      }
    };

    return customRenderer;
  }

  // 初始化解析器
  function initializeParser() {
    if (!containerElement) return;

    // 清理之前的解析器
    if (parser && isParserInitialized) {
      try {
        smd.parser_end(parser);
      } catch (e) {
        // 忽略清理错误
      }
    }

    // 清空容器并重置计数器
    containerElement.innerHTML = '';
    charIndex = 0;
    previousContent = '';
    isParserInitialized = false;

    // 使用自定义渲染器创建新的解析器
    renderer = createCustomRenderer(containerElement);
    parser = smd.parser(renderer);
    isParserInitialized = true;

    // 如果有初始内容，处理它
    if (content) {
      smd.parser_write(parser, content);
      previousContent = content;

      // 如果不是流式传输状态，立即结束解析
      if (!isStreaming) {
        smd.parser_end(parser);
      }

      setTimeout(() => addAnimationToNewText(), 0);
    }
  }

  // 响应内容变化
  $effect(() => {
    if (containerElement) {
      if (content.length < previousContent.length) {
        // 内容被重置或减少，重新初始化
        initializeParser();
      } else if (content.length > previousContent.length) {
        // 内容增加，增量处理
        processNewContent();
      }
    }
  });

  // 响应流式传输状态变化
  $effect(() => {
    if (!isStreaming && parser && isParserInitialized && previousContent === content) {
      // 流式传输结束，调用 parser_end
      endStreaming();
    }
  });

  onMount(() => {
    if (containerElement) {
      initializeParser();
    }
  });

  onDestroy(() => {
    // 清理解析器
    if (parser && isParserInitialized) {
      try {
        smd.parser_end(parser);
      } catch (e) {
        // 忽略清理错误
      }
    }
  });
</script>

<div bind:this={containerElement} class="streaming-markdown">
  <!-- 内容将通过 streaming-markdown 库直接渲染到这里 -->
</div>

<style>
  :global(.streaming-markdown) {
    line-height: 1.7;
    color: #24292f;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
    font-size: 16px;
    background-color: #ffffff;
  }

  :global(.streaming-markdown h1) {
    font-size: 2rem;
    font-weight: 600;
    margin: 24px 0 16px 0;
    border-bottom: 1px solid #d1d9e0;
    padding-bottom: 10px;
    color: #1f2328;
    line-height: 1.25;
  }

  :global(.streaming-markdown h2) {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 24px 0 16px 0;
    border-bottom: 1px solid #d1d9e0;
    padding-bottom: 10px;
    color: #1f2328;
    line-height: 1.25;
  }

  :global(.streaming-markdown h3) {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 24px 0 16px 0;
    color: #1f2328;
    line-height: 1.25;
  }

  :global(.streaming-markdown h4) {
    font-size: 1rem;
    font-weight: 600;
    margin: 24px 0 16px 0;
    color: #1f2328;
    line-height: 1.25;
  }

  :global(.streaming-markdown h5) {
    font-size: 0.875rem;
    font-weight: 600;
    margin: 24px 0 16px 0;
    color: #1f2328;
    line-height: 1.25;
  }

  :global(.streaming-markdown h6) {
    font-size: 0.85rem;
    font-weight: 600;
    margin: 24px 0 16px 0;
    color: #656d76;
    line-height: 1.25;
  }

  :global(.streaming-markdown p) {
    margin: 0 0 16px 0;
    line-height: 1.7;
  }

  :global(.streaming-markdown ul, .streaming-markdown ol) {
    margin: 0 0 16px 0;
    padding-left: 32px;
  }

  :global(.streaming-markdown li) {
    margin: 4px 0;
    line-height: 1.7;
  }

  :global(.streaming-markdown li > p) {
    margin: 16px 0;
  }

  :global(.streaming-markdown blockquote) {
    margin: 0 0 16px 0;
    padding: 0 16px;
    border-left: 4px solid #d1d9e0;
    color: #656d76;
    background-color: #f6f8fa;
    border-radius: 0 6px 6px 0;
  }

  :global(.streaming-markdown blockquote > p) {
    margin: 16px 0;
  }

  :global(.streaming-markdown code) {
    background-color: rgba(175, 184, 193, 0.2);
    padding: 2px 4px;
    border-radius: 6px;
    font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 85%;
    color: #1f2328;
  }

  :global(.streaming-markdown pre) {
    background-color: #f6f8fa;
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 0 0 16px 0;
    border: 1px solid #d1d9e0;
    line-height: 1.45;
    position: relative;
  }

  :global(.code-copy-container:hover .absolute) {
    opacity: 1;
  }

  :global(.code-copy-container .absolute) {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }

  :global(.code-copy-container .absolute:focus) {
    opacity: 1;
  }

  :global(.streaming-markdown pre code) {
    background-color: transparent;
    padding: 0;
    font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 85%;
    line-height: 1.45;
    color: #1f2328;
  }

  /* Highlight.js 样式覆盖 */
  :global(.streaming-markdown .hljs) {
    background: transparent !important;
    padding: 0 !important;
  }

  /* 代码高亮颜色主题 (GitHub 明亮风格) */
  :global(.streaming-markdown .hljs-comment),
  :global(.streaming-markdown .hljs-quote) {
    color: #6e7781;
    font-style: italic;
  }

  :global(.streaming-markdown .hljs-keyword),
  :global(.streaming-markdown .hljs-selector-tag),
  :global(.streaming-markdown .hljs-subst) {
    color: #cf222e;
    font-weight: 600;
  }

  :global(.streaming-markdown .hljs-number),
  :global(.streaming-markdown .hljs-literal),
  :global(.streaming-markdown .hljs-variable),
  :global(.streaming-markdown .hljs-template-variable),
  :global(.streaming-markdown .hljs-tag .hljs-attr) {
    color: #0550ae;
  }

  :global(.streaming-markdown .hljs-string),
  :global(.streaming-markdown .hljs-doctag) {
    color: #0a3069;
  }

  :global(.streaming-markdown .hljs-title),
  :global(.streaming-markdown .hljs-section),
  :global(.streaming-markdown .hljs-selector-id) {
    color: #8250df;
    font-weight: 600;
  }

  :global(.streaming-markdown .hljs-type),
  :global(.streaming-markdown .hljs-class .hljs-title) {
    color: #cf222e;
  }

  :global(.streaming-markdown .hljs-tag),
  :global(.streaming-markdown .hljs-name),
  :global(.streaming-markdown .hljs-attribute) {
    color: #116329;
  }

  :global(.streaming-markdown .hljs-regexp),
  :global(.streaming-markdown .hljs-link) {
    color: #0a3069;
  }

  :global(.streaming-markdown .hljs-symbol),
  :global(.streaming-markdown .hljs-bullet) {
    color: #bc4c00;
  }

  :global(.streaming-markdown .hljs-built_in),
  :global(.streaming-markdown .hljs-builtin-name) {
    color: #0550ae;
  }

  :global(.streaming-markdown .hljs-meta) {
    color: #6e7781;
  }

  :global(.streaming-markdown .hljs-deletion) {
    background: #ffebe9;
    color: #82071e;
  }

  :global(.streaming-markdown .hljs-addition) {
    background: #dafbe1;
    color: #116329;
  }

  :global(.streaming-markdown .hljs-emphasis) {
    font-style: italic;
  }

  :global(.streaming-markdown .hljs-strong) {
    font-weight: 600;
  }

  :global(.streaming-markdown table) {
    border-collapse: collapse;
    width: 100%;
    margin: 0 0 16px 0;
    border-spacing: 0;
    display: block;
    overflow: auto;
  }

  :global(.streaming-markdown th, .streaming-markdown td) {
    border: 1px solid #d1d9e0;
    padding: 6px 13px;
    text-align: left;
  }

  :global(.streaming-markdown th) {
    background-color: #f6f8fa;
    font-weight: 600;
    color: #1f2328;
  }

  :global(.streaming-markdown tr:nth-child(2n)) {
    background-color: #f6f8fa;
  }

  :global(.streaming-markdown a) {
    color: #0969da;
    text-decoration: none;
  }

  :global(.streaming-markdown a:hover) {
    color: #0969da;
    text-decoration: underline;
  }

  :global(.streaming-markdown a:visited) {
    color: #8250df;
  }

  :global(.streaming-markdown strong) {
    font-weight: 600;
    color: #1f2328;
  }

  :global(.streaming-markdown em) {
    font-style: italic;
  }

  :global(.streaming-markdown hr) {
    border: none;
    border-top: 2px solid #d1d9e0;
    margin: 24px 0;
    height: 0.25em;
    background-color: #d1d9e0;
  }

  :global(.streaming-markdown img) {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    margin: 16px 0;
  }

  :global(.streaming-markdown del) {
    text-decoration: line-through;
    color: #656d76;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(3px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  :global(.fade-in-char) {
    display: inline-block;
    opacity: 0;
    animation: fadeIn forwards;
    line-height: 1.7;
  }

</style>
