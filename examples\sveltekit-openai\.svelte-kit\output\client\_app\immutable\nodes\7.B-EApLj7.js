import{t as I,a as c,b as f}from"../chunks/CGanT4Ze.js";import{t as A,g as a,d as k,c as n,s as i,b as s,n as m,r as o}from"../chunks/Cc9lkQ6R.js";import{s as B}from"../chunks/2Hh1nz_V.js";import{S as C}from"../chunks/CrxE95yh.js";import{B as g}from"../chunks/BrOQj_DL.js";var P=I('<main class="container mx-auto p-6 max-w-4xl svelte-ljbuca"><div class="mb-8"><h1 class="text-3xl font-bold mb-4">流式 Markdown 渲染演示</h1> <p class="text-gray-600 mb-6">这个演示展示了如何使用 Svelte 5 + Vercel AI SDK 实现 Markdown 的流式渲染和逐字淡入效果。</p> <div class="flex gap-4 mb-6"><!> <!> <!></div></div> <div class="border rounded-lg p-6 bg-white shadow-sm"><h2 class="text-xl font-semibold mb-4">渲染结果：</h2> <div class="min-h-[200px]"><!></div></div> <div class="mt-8 p-4 bg-gray-50 rounded-lg"><h3 class="text-lg font-semibold mb-2">技术说明：</h3> <ul class="list-disc list-inside space-y-1 text-sm text-gray-700"><li>使用 <code class="svelte-ljbuca">streaming-markdown</code> 库进行流式 Markdown 解析</li> <li>通过 CSS 动画实现逐字淡入效果</li> <li>支持增量内容更新以提高性能</li> <li>正确调用 <code class="svelte-ljbuca">parser_end</code> 函数来结束流并刷新剩余内容</li> <li>乐观解析：内联代码块在流式传输时立即样式化</li> <li>兼容 Svelte 5 的新语法（$props, $state, $effect）</li> <li>可配置动画延迟和持续时间</li> <li>支持文本选择和复制功能</li></ul></div></main>');function q(_){let l=k(""),t=k(!1);const v=`# 流式 Markdown 演示

这是一个**流式 Markdown 渲染**组件的演示。

## 功能特点

- 支持实时 Markdown 解析
- 逐字淡入动画效果
- 高性能增量更新
- 支持所有标准 Markdown 语法

### 代码示例

\`\`\`javascript
function streamingMarkdown() {
  console.log('Hello, streaming world!');
  return 'Amazing!';
}
\`\`\`

### 列表支持

1. 有序列表项 1
2. 有序列表项 2
3. 有序列表项 3

- 无序列表项 A
- 无序列表项 B
- 无序列表项 C

### 引用块

> 这是一个引用块的示例。
> 它可以包含多行内容。
> 
> 甚至可以包含**粗体**和*斜体*文本。

### 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

### 链接和图片

这里有一个[链接示例](https://example.com)。

---

**演示完成！** 🎉`;function M(){if(a(t))return;s(t,!0),s(l,"");let e=0;const d=setInterval(()=>{e<v.length?(s(l,a(l)+v[e]),e++):(clearInterval(d),s(t,!1))},50)}function S(){s(l,""),s(t,!1)}function y(){s(l,v),s(t,!1)}var u=P(),p=n(u),x=i(n(p),4),b=n(x);g(b,{onclick:M,get disabled(){return a(t)},variant:"default",children:(e,d)=>{m();var r=f();A(()=>B(r,a(t)?"流式渲染中...":"开始流式演示")),c(e,r)},$$slots:{default:!0}});var h=i(b,2);g(h,{onclick:y,get disabled(){return a(t)},variant:"secondary",children:(e,d)=>{m();var r=f("显示完整内容");c(e,r)},$$slots:{default:!0}});var j=i(h,2);g(j,{onclick:S,get disabled(){return a(t)},variant:"outline",children:(e,d)=>{m();var r=f("重置");c(e,r)},$$slots:{default:!0}}),o(x),o(p);var w=i(p,2),$=i(n(w),2),D=n($);C(D,{get content(){return a(l)},get isStreaming(){return a(t)},animationDelay:30,fadeInDuration:500}),o($),o(w),m(2),o(u),c(_,u)}export{q as component};
