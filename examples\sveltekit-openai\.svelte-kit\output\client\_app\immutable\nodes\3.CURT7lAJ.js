import{t as b,a as o,c as P,b as H}from"../chunks/CGanT4Ze.js";import{p as mt,t as y,a as pt,c as u,r,s as R,b as tt,d as _t,g as t,u as I,f as A,n as at}from"../chunks/Cc9lkQ6R.js";import{e as gt,s as k}from"../chunks/2Hh1nz_V.js";import{i as v}from"../chunks/0U5e1eCd.js";import{e as et,A as xt,i as ht}from"../chunks/zbNWLoGC.js";import{s as yt}from"../chunks/krzcCheT.js";import{B as Q}from"../chunks/BrOQj_DL.js";import{T as bt}from"../chunks/CEXehBSS.js";import{S as wt}from"../chunks/CrxE95yh.js";import{C as Ct}from"../chunks/U6ZYfsmc.js";var kt=b('<div class="flex flex-col gap-2"> <div class="flex gap-2"><!> <!></div></div>'),It=b('<div class="text-gray-500"> </div>'),St=b('<div class="text-gray-500">Getting location...</div>'),Tt=b('<div class="text-gray-500"> </div>'),Lt=b("<pre> </pre>"),Mt=b('<div class="text-gray-500"> </div>'),Nt=b('<div class="text-gray-500"> </div>'),Dt=b("<div></div>"),Ft=b('<main class="flex flex-col items-center h-dvh w-dvw"><div class="grid h-full w-full max-w-4xl grid-cols-1 grid-rows-[1fr,120px] p-2"><div class="overflow-y-auto w-full h-full"></div> <form class="relative"><p> </p> <div><a href="/chat/1">chat 1</a> <a href="/chat/2">chat 2</a> <a href="/chat/3">chat 3</a></div> <!> <!></form></div></main>');function Et(ot,st){mt(st,!0);const g=new Ct({async onToolCall({toolCall:e}){if(await new Promise(c=>setTimeout(c,2e3)),e.toolName==="getLocation"){const c=["New York","Los Angeles","Chicago","San Francisco"],S=c[Math.floor(Math.random()*c.length)];await g.addToolResult({toolCallId:e.toolCallId,tool:"getLocation",output:S})}}});let Y=_t("");const rt=I(()=>g.status!=="ready");function it(e){return e==="assistant"?"bg-primary text-secondary rounded-md":"bg-secondary text-primary rounded-md justify-self-end"}function U(e){e.preventDefault(),g.sendMessage({text:t(Y)}),tt(Y,"")}var G=Ft(),V=u(G),W=u(V);et(W,21,()=>g.messages,e=>e.id,(e,c)=>{var S=Dt();et(S,21,()=>t(c).parts,ht,(E,a)=>{var Z=P(),vt=A(Z);{var dt=T=>{var $=P(),J=A($);{var K=d=>{const x=I(()=>g.status==="streaming"&&t(c)===g.lastMessage&&(t(a).state==="streaming"||t(a).state===void 0));wt(d,{get content(){return t(a).text},get isStreaming(){return t(x)},animationDelay:20,fadeInDuration:400})},w=d=>{var x=H();y(()=>k(x,t(a).text)),o(d,x)};v(J,d=>{t(c).role==="assistant"?d(K):d(w,!1)})}o(T,$)},ft=(T,$)=>{{var J=w=>{var d=P();const x=I(()=>t(a).toolCallId),B=I(()=>t(a).state);var C=A(d);{var L=i=>{var m=kt();const l=I(()=>t(a).input);var n=u(m),p=R(n),s=u(p);Q(s,{variant:"default",onclick:()=>g.addToolResult({toolCallId:t(x),tool:"askForConfirmation",output:"Yes, confirmed"}),children:(h,N)=>{at();var _=H("Yes");o(h,_)},$$slots:{default:!0}});var f=R(s,2);Q(f,{variant:"secondary",onclick:()=>g.addToolResult({toolCallId:t(x),tool:"askForConfirmation",output:"No, denied"}),children:(h,N)=>{at();var _=H("No");o(h,_)},$$slots:{default:!0}}),r(p),r(m),y(()=>k(n,`${t(l).message??""} `)),o(i,m)},M=(i,m)=>{{var l=n=>{var p=It(),s=u(p,!0);r(p),y(()=>k(s,t(a).output)),o(n,p)};v(i,n=>{t(B)==="output-available"&&n(l)},m)}};v(C,i=>{t(B)==="input-available"?i(L):i(M,!1)})}o(w,d)},K=(w,d)=>{{var x=C=>{var L=P(),M=A(L);{var i=l=>{var n=St();o(l,n)},m=(l,n)=>{{var p=s=>{var f=Tt(),h=u(f);r(f),y(()=>k(h,`Location: ${t(a).output??""}`)),o(s,f)};v(l,s=>{t(a).state==="output-available"&&s(p)},n)}};v(M,l=>{t(a).state==="input-available"?l(i):l(m,!1)})}o(C,L)},B=(C,L)=>{{var M=i=>{var m=P(),l=A(m);{var n=s=>{var f=Lt(),h=u(f,!0);r(f),y(N=>k(h,N),[()=>JSON.stringify(t(a),null,2)]),o(s,f)},p=(s,f)=>{{var h=_=>{var D=Mt();const O=I(()=>t(a).input);var F=u(D);r(D),y(()=>k(F,`Getting weather information for ${t(O).city??""}...`)),o(_,D)},N=(_,D)=>{{var O=F=>{var q=Nt();const ut=I(()=>t(a).input);var ct=u(q);r(q),y(()=>k(ct,`Weather in ${t(ut).city??""}: ${t(a).output??""}`)),o(F,q)};v(_,F=>{t(a).state==="output-available"&&F(O)},D)}};v(s,_=>{t(a).state==="input-available"?_(h):_(N,!1)},f)}};v(l,s=>{t(a).state==="input-streaming"?s(n):s(p,!1)})}o(i,m)};v(C,i=>{t(a).type==="tool-getWeatherInformation"&&i(M)},L)}};v(w,C=>{t(a).type==="tool-getLocation"?C(x):C(B,!1)},d)}};v(T,w=>{t(a).type==="tool-askForConfirmation"?w(J):w(K,!1)},$)}};v(vt,T=>{t(a).type==="text"?T(dt):T(ft,!1)})}o(E,Z)}),r(S),y(E=>yt(S,1,`${E??""} my-2 max-w-[80%] p-2 flex flex-col gap-2`),[()=>it(t(c).role)]),o(e,S)}),r(W);var j=R(W,2),z=u(j),lt=u(z,!0);r(z);var X=R(z,4);bt(X,{placeholder:"Send a message...",class:"h-full",onkeydown:e=>{e.key==="Enter"&&!e.shiftKey&&(e.preventDefault(),U(e))},get value(){return t(Y)},set value(e){tt(Y,e,!0)}});var nt=R(X,2);Q(nt,{"aria-label":"Send message",get disabled(){return t(rt)},type:"submit",size:"icon",class:"absolute right-3 bottom-3",children:(e,c)=>{xt(e,{})},$$slots:{default:!0}}),r(j),r(V),r(G),y(()=>k(lt,g.status)),gt("submit",j,U),o(ot,G),pt()}export{Et as component};
