import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types.js';
import { configManager } from '$lib/config/config-manager.js';

// GET /api/llm-config - 获取LLM配置
export const GET: RequestHandler = async () => {
  try {
    const config = configManager.getConfig();
    return json({
      success: true,
      data: config,
    });
  } catch (error) {
    console.error('获取LLM配置时出错:', error);
    return json(
      {
        success: false,
        error: '获取配置失败',
      },
      { status: 500 },
    );
  }
};

// POST /api/llm-config - 更新LLM配置
export const POST: RequestHandler = async ({
  request,
}: {
  request: Request;
}) => {
  try {
    const newConfig = await request.json();
    configManager.setConfig(newConfig);

    return json({
      success: true,
      message: '配置更新成功',
    });
  } catch (error) {
    console.error('更新LLM配置时出错:', error);
    return json(
      {
        success: false,
        error: '更新配置失败',
      },
      { status: 500 },
    );
  }
};
