import{t as De,a as Le}from"./CGanT4Ze.js";import{o as Re,a as Oe}from"./xvKp5MAS.js";import{p as qe,j as fe,a as ye,g as k,d as Se,b as Be}from"./Cc9lkQ6R.js";import{p as y,b as He}from"./0U5e1eCd.js";const K=1,j=2,ne=3,te=4,ie=5,ae=6,ce=7,se=8,M=9,P=10,J=11,U=12,T=13,I=14,D=15,L=16,w=17,V=18,g=19,R=20,b=21,me=22,O=23,W=24,N=25,be=26,Z=27,v=28,G=29,_=30,f=31,S=101,ke=102,Ee=103,_e=104,$=105,p=1,xe=2,Ae=4,we=8,Ne=16;function Ue(e){switch(e){case p:return"href";case xe:return"src";case Ae:return"class";case we:return"checked";case Ne:return"start"}}const ve=e=>{switch(e){case 1:return ne;case 2:return te;case 3:return ie;case 4:return ae;case 5:return ce;default:return se}},Ge=ve,he=24;function Ke(e){const i=new Uint32Array(he);return i[0]=K,{renderer:e,text:"",pending:"",tokens:i,len:0,token:K,fence_end:0,blockquote_idx:0,hr_char:"",hr_chars:0,fence_start:0,spaces:new Uint8Array(he),indent:"",indent_len:0,table_state:0}}function B(e){e.pending.length>0&&l(e,`
`)}function c(e){e.text.length!==0&&(console.assert(e.len>0,"Never adding text to root"),e.renderer.add_text(e.renderer.data,e.text),e.text="")}function o(e){console.assert(e.len>0,"No nodes to end"),e.len-=1,e.token=e.tokens[e.len],e.renderer.end_token(e.renderer.data)}function s(e,i){(e.tokens[e.len]===W||e.tokens[e.len]===O)&&i!==N&&o(e),e.len+=1,e.tokens[e.len]=i,e.token=i,e.renderer.add_token(e.renderer.data,i)}function Me(e,i,n){for(;n<=e.len;){if(e.tokens[n]===i)return n;n+=1}return-1}function A(e,i){for(e.fence_start=0;e.len>i;)o(e)}function H(e,i){let n=0;for(let t=0;t<=e.len&&(i-=e.spaces[t],!(i<0));t+=1)switch(e.tokens[t]){case M:case P:case R:case N:n=t;break}for(;e.len>n;)o(e);return i}function z(e,i){let n=-1,t=-1;for(let r=e.blockquote_idx+1;r<=e.len;r+=1)if(e.tokens[r]===N){if(e.indent_len<e.spaces[r]){t=-1;break}t=r}else e.tokens[r]===i&&(n=r);return t===-1?n===-1?(A(e,e.blockquote_idx),s(e,i),!0):(A(e,n),!1):(A(e,t),s(e,i),!0)}function X(e,i){s(e,N),e.spaces[e.len]=e.indent_len+i,h(e),e.token=Ee}function h(e){e.indent="",e.indent_len=0,e.pending=""}function ee(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return!0;default:return!1}}function Pe(e){switch(e){case 32:case 58:case 59:case 41:case 44:case 33:case 46:case 63:case 93:case 10:return!0;default:return!1}}function We(e){return ee(e)||Pe(e)}function l(e,i){for(const n of i){if(e.token===S){switch(n){case" ":e.indent_len+=1;continue;case"	":e.indent_len+=4;continue}let r=H(e,e.indent_len);e.indent_len=0,e.token=e.tokens[e.len],r>0&&l(e," ".repeat(r))}const t=e.pending+n;switch(e.token){case b:case K:case R:case W:case O:switch(console.assert(e.text.length===0,"Root should not have any text"),e.pending[0]){case void 0:e.pending=n;continue;case" ":console.assert(e.pending.length===1),e.pending=n,e.indent+=" ",e.indent_len+=1;continue;case"	":console.assert(e.pending.length===1),e.pending=n,e.indent+="	",e.indent_len+=4;continue;case`
`:if(console.assert(e.pending.length===1),e.tokens[e.len]===N&&e.token===b){o(e),h(e),e.pending=n;continue}A(e,e.blockquote_idx),h(e),e.blockquote_idx=0,e.fence_start=0,e.pending=n;continue;case"#":switch(n){case"#":if(e.pending.length<6){e.pending=t;continue}break;case" ":H(e,e.indent_len),s(e,Ge(e.pending.length)),h(e);continue}break;case">":{const a=Me(e,R,e.blockquote_idx+1);a===-1?(A(e,e.blockquote_idx),e.blockquote_idx+=1,e.fence_start=0,s(e,R)):e.blockquote_idx=a,h(e),e.pending=n;continue}case"-":case"*":case"_":if(e.hr_chars===0&&(console.assert(e.pending.length===1,"Pending should be one character"),e.hr_chars=1,e.hr_char=e.pending),e.hr_chars>0){switch(n){case e.hr_char:e.hr_chars+=1,e.pending=t;continue;case" ":e.pending=t;continue;case`
`:if(e.hr_chars<3)break;H(e,e.indent_len),e.renderer.add_token(e.renderer.data,me),e.renderer.end_token(e.renderer.data),h(e),e.hr_chars=0;continue}e.hr_chars=0}if(e.pending[0]!=="_"&&e.pending[1]===" "){z(e,O),X(e,2),l(e,t.slice(2));continue}break;case"`":if(e.pending.length<3){if(n==="`"){e.pending=t,e.fence_start=t.length;continue}e.fence_start=0;break}switch(n){case"`":e.pending.length===e.fence_start?(e.pending=t,e.fence_start=t.length):(s(e,j),h(e),e.fence_start=0,l(e,t));continue;case`
`:{H(e,e.indent_len),s(e,P),e.pending.length>e.fence_start&&e.renderer.set_attr(e.renderer.data,Ae,e.pending.slice(e.fence_start)),h(e),e.token=S;continue}default:e.pending=t;continue}case"+":if(n!==" ")break;z(e,O),X(e,2);continue;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":if(e.pending[e.pending.length-1]==="."){if(n!==" ")break;z(e,W)&&e.pending!=="1."&&e.renderer.set_attr(e.renderer.data,Ne,e.pending.slice(0,-1)),X(e,e.pending.length+1);continue}else{const a=n.charCodeAt(0);if(a===46||ee(a)){e.pending=t;continue}}break;case"|":A(e,e.blockquote_idx),s(e,Z),s(e,v),e.pending="",l(e,n);continue}let r=t;if(e.token===b)e.token=e.tokens[e.len],e.renderer.add_token(e.renderer.data,b),e.renderer.end_token(e.renderer.data);else if(e.indent_len>=4){let a=0;for(;a<4;a+=1)if(e.indent[a]==="	"){a=a+1;break}r=e.indent.slice(a)+t,s(e,M)}else s(e,j);h(e),l(e,r);continue;case Z:if(e.table_state===1)switch(n){case"-":case" ":case"|":case":":e.pending=t;continue;case`
`:e.table_state=2,e.pending="";continue;default:o(e),e.table_state=0;break}else switch(e.pending){case"|":s(e,v),e.pending="",l(e,n);continue;case`
`:o(e),e.pending="",e.table_state=0,l(e,n);continue}break;case v:switch(e.pending){case"":break;case"|":s(e,G),o(e),e.pending="",l(e,n);continue;case`
`:o(e),e.table_state=Math.min(e.table_state+1,2),e.pending="",l(e,n);continue;default:s(e,G),l(e,n);continue}break;case G:if(e.pending==="|"){c(e),o(e),e.pending="",l(e,n);continue}break;case M:switch(t){case`
    `:case`
   	`:case`
  	`:case`
 	`:case`
	`:e.text+=`
`,e.pending="";continue;case`
`:case`
 `:case`
  `:case`
   `:e.pending=t;continue;default:e.pending.length!==0?(c(e),o(e),e.pending=n):e.text+=n;continue}case P:switch(n){case"`":e.pending=t;continue;case`
`:if(t.length===e.fence_start+e.fence_end+1){c(e),o(e),e.pending="",e.fence_start=0,e.fence_end=0,e.token=S;continue}e.token=S;break;case" ":if(e.pending[0]===`
`){e.pending=t,e.fence_end+=1;continue}break}e.text+=e.pending,e.pending=n,e.fence_end=1;continue;case J:switch(n){case"`":t.length===e.fence_start+ +(e.pending[0]===" ")?(c(e),o(e),e.pending="",e.fence_start=0):e.pending=t;continue;case`
`:e.text+=e.pending,e.pending="",e.token=b,e.blockquote_idx=0,c(e);continue;case" ":e.text+=e.pending,e.pending=n;continue;default:e.text+=t,e.pending="";continue}case Ee:switch(e.pending.length){case 0:if(n!=="[")break;e.pending=t;continue;case 1:if(n!==" "&&n!=="x")break;e.pending=t;continue;case 2:if(n!=="]")break;e.pending=t;continue;case 3:if(n!==" ")break;e.renderer.add_token(e.renderer.data,be),e.pending[1]==="x"&&e.renderer.set_attr(e.renderer.data,we,""),e.renderer.end_token(e.renderer.data),e.pending=" ";continue}e.token=e.tokens[e.len],e.pending="",l(e,t);continue;case I:case D:{let a="*",d=U;if(e.token===D&&(a="_",d=T),a===e.pending){if(c(e),a===n){o(e),e.pending="";continue}s(e,d),e.pending=n;continue}break}case U:case T:{let a="*",d=I;switch(e.token===T&&(a="_",d=D),e.pending){case a:a===n?e.tokens[e.len-1]===d?e.pending=t:(c(e),s(e,d),e.pending=""):(c(e),o(e),e.pending=n);continue;case a+a:const u=e.token;c(e),o(e),o(e),a!==n?(s(e,u),e.pending=n):e.pending="";continue}break}case L:if(t==="~~"){c(e),o(e),e.pending="";continue}break;case $:n===`
`?(c(e),s(e,_),e.pending=""):(e.token=e.tokens[e.len],e.pending[0]==="\\"?e.text+="[":e.text+="$$",e.pending="",l(e,n));continue;case _:if(t==="\\]"||t==="$$"){c(e),o(e),e.pending="";continue}break;case f:if(t==="\\)"||e.pending[0]==="$"){c(e),o(e),n===")"?e.pending="":e.pending=n;continue}break;case ke:t==="http://"||t==="https://"?(c(e),s(e,V),e.pending=t,e.text=t):"http:/"[e.pending.length]===n||"https:/"[e.pending.length]===n?e.pending=t:(e.token=e.tokens[e.len],l(e,n));continue;case w:case g:if(e.pending==="]"){c(e),n==="("?e.pending=t:(o(e),e.pending=n);continue}if(e.pending[0]==="]"&&e.pending[1]==="("){if(n===")"){const a=e.token===w?p:xe,d=e.pending.slice(2);e.renderer.set_attr(e.renderer.data,a,d),o(e),e.pending=""}else e.pending+=n;continue}break;case V:n===" "||n===`
`||n==="\\"?(e.renderer.set_attr(e.renderer.data,p,e.pending),c(e),o(e),e.pending=n):(e.text+=n,e.pending=t);continue;case _e:if(t.startsWith("<br")){if(t.length===3||n===" "||n==="/"&&(t.length===4||e.pending[e.pending.length-1]===" ")){e.pending=t;continue}if(n===">"){c(e),e.token=e.tokens[e.len],e.renderer.add_token(e.renderer.data,b),e.renderer.end_token(e.renderer.data),e.pending="";continue}}e.token=e.tokens[e.len],e.text+="<",e.pending=e.pending.slice(1),l(e,n);continue}switch(e.pending[0]){case"\\":if(e.token===g||e.token===_||e.token===f)break;switch(n){case"(":c(e),s(e,f),e.pending="";continue;case"[":e.token=$,e.pending=t;continue;case`
`:e.pending=n;continue;default:let r=n.charCodeAt(0);e.pending="",e.text+=ee(r)||r>=65&&r<=90||r>=97&&r<=122?t:n;continue}case`
`:switch(e.token){case g:case _:case f:break;case ne:case te:case ie:case ae:case ce:case se:c(e),A(e,e.blockquote_idx),e.blockquote_idx=0,e.pending=n;continue;default:c(e),e.pending=n,e.token=b,e.blockquote_idx=0;continue}break;case"<":if(e.token!==g&&e.token!==_&&e.token!==f){c(e),e.pending=t,e.token=_e;continue}break;case"`":if(e.token===g)break;n==="`"?(e.fence_start+=1,e.pending=t):(e.fence_start+=1,c(e),s(e,J),e.text=n===" "||n===`
`?"":n,e.pending="");continue;case"_":case"*":{if(e.token===g||e.token===_||e.token===f||e.token===I)break;let r=U,a=I;const d=e.pending[0];if(d==="_"&&(r=T,a=D),e.pending.length===1){if(d===n){e.pending=t;continue}if(n!==" "&&n!==`
`){c(e),s(e,r),e.pending=n;continue}}else{if(d===n){c(e),s(e,a),s(e,r),e.pending="";continue}if(n!==" "&&n!==`
`){c(e),s(e,a),e.pending=n;continue}}break}case"~":if(e.token!==g&&e.token!==L){if(e.pending==="~"){if(n==="~"){e.pending=t;continue}}else if(n!==" "&&n!==`
`){c(e),s(e,L),e.pending=n;continue}}break;case"$":if(e.token!==g&&e.token!==L&&e.pending==="$")if(n==="$"){e.token=$,e.pending=t;continue}else{if(We(n.charCodeAt(0)))break;c(e),s(e,f),e.pending=n;continue}break;case"[":if(e.token!==g&&e.token!==w&&e.token!==_&&e.token!==f&&n!=="]"){c(e),s(e,w),e.pending=n;continue}break;case"!":if(e.token!==g&&n==="["){c(e),s(e,g),e.pending="";continue}break;case" ":if(e.pending.length===1&&n===" ")continue;break}if(e.token!==g&&e.token!==w&&e.token!==_&&e.token!==f&&n==="h"&&(e.pending===" "||e.pending==="")){e.text+=e.pending,e.pending=n,e.token=ke;continue}e.text+=e.pending,e.pending=n}c(e)}function Ye(e){return{add_token:Fe,end_token:Qe,add_text:$e,set_attr:ze,data:{nodes:[e,,,,,],index:0}}}function Fe(e,i){var r;let n=e.nodes[e.index],t;switch(i){case K:return;case R:t=document.createElement("blockquote");break;case j:t=document.createElement("p");break;case b:t=document.createElement("br");break;case me:t=document.createElement("hr");break;case ne:t=document.createElement("h1");break;case te:t=document.createElement("h2");break;case ie:t=document.createElement("h3");break;case ae:t=document.createElement("h4");break;case ce:t=document.createElement("h5");break;case se:t=document.createElement("h6");break;case U:case T:t=document.createElement("em");break;case I:case D:t=document.createElement("strong");break;case L:t=document.createElement("s");break;case J:t=document.createElement("code");break;case V:case w:t=document.createElement("a");break;case g:t=document.createElement("img");break;case O:t=document.createElement("ul");break;case W:t=document.createElement("ol");break;case N:t=document.createElement("li");break;case be:let a=t=document.createElement("input");a.type="checkbox",a.disabled=!0;break;case M:case P:n=n.appendChild(document.createElement("pre")),t=document.createElement("code");break;case Z:t=document.createElement("table");break;case v:switch(n.children.length){case 0:n=n.appendChild(document.createElement("thead"));break;case 1:n=n.appendChild(document.createElement("tbody"));break;default:n=n.children[1]}t=document.createElement("tr");break;case G:t=document.createElement(((r=n.parentElement)==null?void 0:r.tagName)==="THEAD"?"th":"td");break;case _:t=document.createElement("equation-block");break;case f:t=document.createElement("equation-inline");break}e.nodes[++e.index]=n.appendChild(t)}function Qe(e){e.index-=1}function $e(e,i){e.nodes[e.index].appendChild(document.createTextNode(i))}function ze(e,i,n){e.nodes[e.index].setAttribute(Ue(i),n)}var Xe=De('<div class="streaming-markdown"></div>');function pe(e,i){qe(i,!0);let n=y(i,"content",3,""),t=y(i,"animationDelay",3,20),r=y(i,"fadeInDuration",3,400),a=y(i,"isStreaming",3,!1),d=Se(void 0),u,re,E="",Y=0,x=!1;function F(){if(!k(d))return;const m=document.createTreeWalker(k(d),NodeFilter.SHOW_TEXT,null);let le;for(;le=m.nextNode();){const Q=le,q=Q.parentElement;if(q&&!q.classList.contains("animated")){q.classList.add("animated");const Ie=Q.textContent||"",ue=document.createDocumentFragment();Ie.split("").forEach(ge=>{const C=document.createElement("span");C.className="fade-in-char",C.textContent=ge===" "?" ":ge,C.style.animationDelay=`${Y*t()}ms`,C.style.animationDuration=`${r()}ms`,ue.appendChild(C),Y++}),q.replaceChild(ue,Q)}}}function Ce(){if(!u||!k(d)||!x)return;const m=n().slice(E.length);m&&(l(u,m),E=n(),setTimeout(()=>F(),0))}function Te(){u&&x&&(B(u),setTimeout(()=>F(),0))}function de(){if(k(d)){if(u&&x)try{B(u)}catch{}k(d).innerHTML="",Y=0,E="",x=!1,re=Ye(k(d)),u=Ke(re),x=!0,n()&&(l(u,n()),E=n(),a()||B(u),setTimeout(()=>F(),0))}}fe(()=>{k(d)&&(n().length<E.length?de():n().length>E.length&&Ce())}),fe(()=>{!a()&&u&&x&&E===n()&&Te()}),Re(()=>{k(d)&&de()}),Oe(()=>{if(u&&x)try{B(u)}catch{}});var oe=Xe();He(oe,m=>Be(d,m),()=>k(d)),Le(e,oe),ye()}export{pe as S};
