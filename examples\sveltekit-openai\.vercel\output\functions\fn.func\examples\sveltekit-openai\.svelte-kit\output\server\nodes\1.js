

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.BJQybKyk.js","_app/immutable/chunks/CGanT4Ze.js","_app/immutable/chunks/Cc9lkQ6R.js","_app/immutable/chunks/CsOTzF8v.js","_app/immutable/chunks/2Hh1nz_V.js","_app/immutable/chunks/CRU_BPGW.js","_app/immutable/chunks/Du-VyJey.js","_app/immutable/chunks/BHIRU4QP.js","_app/immutable/chunks/xvKp5MAS.js"];
export const stylesheets = [];
export const fonts = [];
