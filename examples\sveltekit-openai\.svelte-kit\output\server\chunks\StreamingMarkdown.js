import "clsx";
import { m as current_component, c as pop, p as push } from "./context.js";
function onDestroy(fn) {
  var context = (
    /** @type {Component} */
    current_component
  );
  (context.d ??= []).push(fn);
}
function StreamingMarkdown($$payload, $$props) {
  push();
  let {
    content = "",
    animationDelay = 20,
    fadeInDuration = 400,
    isStreaming = false
  } = $$props;
  onDestroy(() => {
  });
  $$payload.out += `<div class="streaming-markdown"></div>`;
  pop();
}
export {
  StreamingMarkdown as S
};
