# Implementation Plan

## Overview
Modify the markdown-chat page to remove the model selection interface and add a horizontal list of favorite models above the input area. The list shows favorite models as text buttons, with an expand button on the right that reveals all models grouped by provider when clicked.

## Types
Add a `favorite` boolean field to the Model interface to indicate which models are user favorites.

```typescript
export interface Model {
  name: string;
  display_name: string;
  max_tokens: number;
  capabilities: LanguageModelCapabilities;
  favorite?: boolean; // 新增字段，标记是否为收藏模型
}
```

## Files
- Modify `src/lib/config/types.ts` - Add favorite field to Model interface
- Modify `src/lib/config/default-config.json` - Add favorite field to some models
- Modify `src/routes/markdown-chat/+page.svelte` - Add horizontal favorite model list and expand functionality

## Functions
- Add `getFavoriteModels()` method to ConfigManager class to filter favorite models
- Add `getAllModelsGroupedByProvider()` method to ConfigManager class to group all models by provider

## Classes
- Modify `ConfigManager` class in `src/lib/config/config-manager.ts`:
  - Add `getFavoriteModels()` method
  - Add `getAllModelsGroupedByProvider()` method

## Dependencies
No new dependencies required. Using existing CSS classes and Svelte components.

## Testing
- Test that favorite models display correctly in horizontal list
- Test that expand button shows all models grouped by provider
- Test that clicking favorite models switches the active model
- Test responsive behavior on different screen sizes

## Implementation Order
1. Update type definitions to include favorite field
2. Update default configuration with favorite models
3. Extend ConfigManager with new methods
4. Modify markdown-chat page to add favorite model list
5. Implement expand/collapse functionality
6. Style the components using existing CSS classes
7. Test the implementation
