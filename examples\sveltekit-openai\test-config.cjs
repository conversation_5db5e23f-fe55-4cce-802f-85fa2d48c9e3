const fs = require('fs');
const path = require('path');

// 读取配置文件
const configPath = path.join(__dirname, 'src', 'lib', 'config', 'default-config.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

console.log('LLM配置测试');
console.log('====================');

// 输出配置信息
console.log('语言模型配置:');
console.log(JSON.stringify(config, null, 2));

// 测试特定模型
console.log('\n\n可用模型列表:');
if (config.language_models && config.language_models.openai && config.language_models.openai.available_models) {
  config.language_models.openai.available_models.forEach((model, index) => {
    console.log(`${index + 1}. ${model.display_name} (${model.name})`);
    console.log(`   最大令牌数: ${model.max_tokens}`);
    console.log(`   功能: ${JSON.stringify(model.capabilities)}`);
  });
}

console.log('\n测试完成!');
