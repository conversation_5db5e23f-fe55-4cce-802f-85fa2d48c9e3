import { env } from '$env/dynamic/private';
import { createOpenAICompatible } from '@ai-sdk/openai-compatible';
import { streamText, smoothStream, stepCountIs, convertToModelMessages } from 'ai';
import { z } from 'zod/v4';
const openai = createOpenAICompatible({
  name: "KimiK2",
  apiKey: "sk-kro0rwMTQQOVnik13gL0gtUtO2M0Xs46qTovInLa4qZAUiLM",
  baseURL: "https://api.moonshot.cn/v1",
  
});
const qwen = createOpenAICompatible({
  name: "<PERSON>wen",
  apiKey: "sk-4d91a13dfe9545fa844abbf1397a1391",
  baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
  
});
// 简化的实时流处理函数（备用方案）
function createRealTimeStream(originalStream: ReadableStream) {
  return originalStream.pipeThrough(new TransformStream({
    transform(chunk, controller) {
      controller.enqueue(chunk);
    }
  }));
}

export const POST = async ({ request }: { request: Request }) => {
  const { messages } = await request.json();

  const result = streamText({
    model: qwen('qwen3-30b-a3b'),
    messages: convertToModelMessages(messages),
    
    
    stopWhen: stepCountIs(5), // multi-steps for server-side tools
    tools: {
      // server-side tool with execute function:
      getWeatherInformation: {
        description: 'show the weather in a given city to the user',
        inputSchema: z.object({ city: z.string() }),
        execute: async ({ city: _ }: { city: string }) => {
          // Add artificial delay of 2 seconds
          await new Promise(resolve => setTimeout(resolve, 2000));

          const weatherOptions = ['sunny', 'cloudy', 'rainy', 'snowy', 'windy'];
          return weatherOptions[
            Math.floor(Math.random() * weatherOptions.length)
          ];
        },
      },
      // client-side tool that starts user interaction:
      askForConfirmation: {
        description: 'Ask the user for confirmation.',
        inputSchema: z.object({
          message: z.string().describe('The message to ask for confirmation.'),
        }),
      },
      // client-side tool that is automatically executed on the client:
      getLocation: {
        description:
          'Get the user location. Always ask for confirmation before using this tool.',
        inputSchema: z.object({}),
      },
    },
    onChunk: (chunk) => {
      console.log(chunk);
    },
    onError: error => {
      console.error(error);
    },
  });
  
  // 🚀 实时流输出方案选择：

  // 方案一：最实时的纯文本流（仅适用于纯文本前端）
  // return new Response(result.textStream.pipeThrough(new TextEncoderStream()), {
  //   headers: {
  //     'Content-Type': 'text/plain; charset=utf-8',
  //     'Cache-Control': 'no-cache',
  //     'Connection': 'keep-alive',
  //     'X-Accel-Buffering': 'no', // 禁用代理服务器缓冲
  //   },
  // });

  // 方案二：使用 toTextStreamResponse（简单且实时）
  // return result.toTextStreamResponse({
  //   headers: {
  //     'Cache-Control': 'no-cache',
  //     'X-Accel-Buffering': 'no',
  //   },
  // });

  // 方案三：保持 UI 消息格式但优化响应头（推荐，兼容前端）
  return result.toUIMessageStreamResponse();

  // 方案四：使用默认的 toUIMessageStreamResponse（标准方案）
  // return result.toUIMessageStreamResponse();
};
