<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { configManager } from '$lib/config/config-manager.js';
  import type { Model } from '$lib/config/types.js';
  import { cn } from '$lib/utils.js';
  import * as DropdownMenu from 'bits-ui/dropdown-menu';
  import Button from '$lib/components/ui/button/button.svelte';

  const dispatch = createEventDispatcher();

  // 数据
  const allModelsGrouped: Record<string, Model[]> = configManager.getAllModelsGroupedByProvider();
  let current = $state(configManager.getCurrentModel());

  function selectModel(provider: string, model: Model) {
    configManager.setCurrentModel({ provider, name: model.name });
    current = configManager.getCurrentModel();
    dispatch('select', { provider, model });
  }

  // 计算当前显示的标题
  function currentTitle() {
    if (!current) return '设置模型';
    const m = configManager.getModelByName(current.provider, current.name);
    return m?.display_name ?? `${current.provider}/${current.name}`;
  }
</script>

<!-- 触发按钮样式，复刻截图外观 -->
<DropdownMenu.Root let:open>
  <DropdownMenu.Trigger asChild>
    <button class="w-full max-w-[260px] flex items-center justify-between gap-2 rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm text-gray-800 shadow-sm hover:bg-gray-50">
      <span class="truncate">{current ? currentTitle() : '最佳'}</span>
      <svg class="size-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.25a.75.75 0 01-1.06 0L5.25 8.29a.75.75 0 01-.02-1.08z" clip-rule="evenodd"/></svg>
    </button>
  </DropdownMenu.Trigger>

  <DropdownMenu.Content class="w-[320px] rounded-xl border border-gray-200 bg-white p-2 shadow-lg">
    <!-- 顶部标签“最佳” -->
    <div class="px-3 py-2 text-sm font-semibold text-teal-700 flex items-center justify-between">
      <span>最佳</span>
      <span class="text-gray-400">▾</span>
    </div>

    <!-- 列表：展示部分示例项（按提供商顺序第一组的前几个作为示例）-->
    {#each Object.entries(allModelsGrouped) as [provider, models], i (provider)}
      {#if i === 0}
        {#each models as model (model.name)}
          <button class={cn('w-full text-left px-3 py-2 text-gray-800 rounded-md hover:bg-gray-50 flex items-center justify-between', current && current.provider === provider && current.name === model.name ? 'bg-gray-50' : '')}
            onclick={() => selectModel(provider, model)}>
            <span>{model.display_name}</span>
            {#if current && current.provider === provider && current.name === model.name}
              <!-- 勾选图标 -->
              <svg class="size-4 text-teal-600" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.704 5.29a1 1 0 010 1.415l-7.25 7.251a1 1 0 01-1.414 0L3.296 9.963A1 1 0 114.71 8.549l3.038 3.039 6.543-6.542a1 1 0 011.414 0z" clip-rule="evenodd"/></svg>
            {/if}
          </button>
        {/each}
      {/if}
    {/each}

    <div class="my-2 mx-2 h-px bg-gray-200"></div>
    <div class="px-3 py-2 text-xs text-gray-500">推荐</div>

    {#each Object.entries(allModelsGrouped) as [provider, models] (provider+'-rest')}
      {#each models as model (model.name+'-rest')}
        <button class="w-full text-left px-3 py-2 text-gray-800 rounded-md hover:bg-gray-50 flex items-center justify-between" onclick={() => selectModel(provider, model)}>
          <span>{model.display_name}</span>
          {#if current && current.provider === provider && current.name === model.name}
            <svg class="size-4 text-teal-600" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.704 5.29a1 1 0 010 1.415l-7.25 7.251a1 1 0 01-1.414 0L3.296 9.963A1 1 0 114.71 8.549l3.038 3.039 6.543-6.542a1 1 0 011.414 0z" clip-rule="evenodd"/></svg>
          {/if}
        </button>
      {/each}
    {/each}

    {#if !current}
      <div class="mt-2 rounded-md bg-yellow-50 px-3 py-2 text-xs text-yellow-800">未设置默认模型，请从上方列表选择。</div>
    {/if}
  </DropdownMenu.Content>
</DropdownMenu.Root>

<style>
  /* 简单对齐图中风格：留白、分隔线、圆角、阴影 */
</style>

