import { e as escape_html } from "../../../chunks/context.js";
import "clsx";
import { S as StreamingMarkdown } from "../../../chunks/StreamingMarkdown.js";
import { B as Button } from "../../../chunks/button.js";
function _page($$payload) {
  let content = "";
  let isStreaming = false;
  const sampleMarkdown = `# 流式 Markdown 演示

这是一个**流式 Markdown 渲染**组件的演示。

## 功能特点

- 支持实时 Markdown 解析
- 逐字淡入动画效果
- 高性能增量更新
- 支持所有标准 Markdown 语法

### 代码示例

\`\`\`javascript
function streamingMarkdown() {
  console.log('Hello, streaming world!');
  return 'Amazing!';
}
\`\`\`

### 列表支持

1. 有序列表项 1
2. 有序列表项 2
3. 有序列表项 3

- 无序列表项 A
- 无序列表项 B
- 无序列表项 C

### 引用块

> 这是一个引用块的示例。
> 它可以包含多行内容。
> 
> 甚至可以包含**粗体**和*斜体*文本。

### 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

### 链接和图片

这里有一个[链接示例](https://example.com)。

---

**演示完成！** 🎉`;
  function startStreaming() {
    if (isStreaming) return;
    isStreaming = true;
    content = "";
    let index = 0;
    const interval = setInterval(
      () => {
        if (index < sampleMarkdown.length) {
          content += sampleMarkdown[index];
          index++;
        } else {
          clearInterval(interval);
          isStreaming = false;
        }
      },
      50
    );
  }
  function resetDemo() {
    content = "";
    isStreaming = false;
  }
  function showFullContent() {
    content = sampleMarkdown;
    isStreaming = false;
  }
  $$payload.out += `<main class="container mx-auto p-6 max-w-4xl svelte-ljbuca"><div class="mb-8"><h1 class="text-3xl font-bold mb-4">流式 Markdown 渲染演示</h1> <p class="text-gray-600 mb-6">这个演示展示了如何使用 Svelte 5 + Vercel AI SDK 实现 Markdown 的流式渲染和逐字淡入效果。</p> <div class="flex gap-4 mb-6">`;
  Button($$payload, {
    onclick: startStreaming,
    disabled: isStreaming,
    variant: "default",
    children: ($$payload2) => {
      $$payload2.out += `<!---->${escape_html(isStreaming ? "流式渲染中..." : "开始流式演示")}`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    onclick: showFullContent,
    disabled: isStreaming,
    variant: "secondary",
    children: ($$payload2) => {
      $$payload2.out += `<!---->显示完整内容`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    onclick: resetDemo,
    disabled: isStreaming,
    variant: "outline",
    children: ($$payload2) => {
      $$payload2.out += `<!---->重置`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div> <div class="border rounded-lg p-6 bg-white shadow-sm"><h2 class="text-xl font-semibold mb-4">渲染结果：</h2> <div class="min-h-[200px]">`;
  StreamingMarkdown($$payload, {
    content,
    isStreaming,
    animationDelay: 30,
    fadeInDuration: 500
  });
  $$payload.out += `<!----></div></div> <div class="mt-8 p-4 bg-gray-50 rounded-lg"><h3 class="text-lg font-semibold mb-2">技术说明：</h3> <ul class="list-disc list-inside space-y-1 text-sm text-gray-700"><li>使用 <code class="svelte-ljbuca">streaming-markdown</code> 库进行流式 Markdown 解析</li> <li>通过 CSS 动画实现逐字淡入效果</li> <li>支持增量内容更新以提高性能</li> <li>正确调用 <code class="svelte-ljbuca">parser_end</code> 函数来结束流并刷新剩余内容</li> <li>乐观解析：内联代码块在流式传输时立即样式化</li> <li>兼容 Svelte 5 的新语法（$props, $state, $effect）</li> <li>可配置动画延迟和持续时间</li> <li>支持文本选择和复制功能</li></ul></div></main>`;
}
export {
  _page as default
};
