#!/usr/bin/env node

/**
 * 测试脚本：用于测试LLM配置的读取和设置功能
 * 这个脚本将演示如何使用configManager来管理LLM配置
 */

import { configManager } from './config-manager.js';
import type { Model } from './types.js';

// 测试函数
async function runTests() {
  console.log('🧪 开始测试LLM配置管理功能...\n');

  try {
    // 1. 读取当前配置
    console.log('1. 读取当前配置...');
    const currentConfig = configManager.getConfig();
    console.log('✅ 当前配置:');
    console.log(JSON.stringify(currentConfig, null, 2));
    console.log('');

    // 2. 获取所有可用模型
    console.log('2. 获取所有可用模型...');
    const allModels = configManager.getAllModels();
    console.log(`✅ 找到 ${allModels.length} 个可用模型:`);
    allModels.forEach(
      (item: { provider: string; model: Model }, index: number) => {
        console.log(
          `   ${index + 1}. ${item.model.display_name} (${item.provider}/${item.model.name})`,
        );
      },
    );
    console.log('');

    // 3. 更新配置测试
    console.log('3. 更新配置测试...');
    const updatedConfig = {
      ...currentConfig,
      language_models: {
        ...currentConfig.language_models,
        openai: {
          ...currentConfig.language_models.openai,
          api_url: 'https://api.openai.com/v1',
          api_key: 'sk-test-key-12345',
        },
      },
    };

    configManager.setConfig(updatedConfig);
    console.log('✅ 配置更新成功');

    // 4. 验证更新后的配置
    console.log('4. 验证更新后的配置...');
    const newConfig = configManager.getConfig();
    console.log('✅ 更新后的配置:');
    console.log(JSON.stringify(newConfig, null, 2));
    console.log('');

    // 5. 测试获取特定提供商的模型
    console.log('5. 测试获取特定提供商的模型...');
    const openaiModels = configManager.getModelsByProvider('openai');
    console.log(`✅ OpenAI 提供商有 ${openaiModels.length} 个模型:`);
    openaiModels.forEach(
      (item: { provider: string; model: Model }, index: number) => {
        console.log(
          `   ${index + 1}. ${item.model.display_name} (${item.model.name})`,
        );
      },
    );
    console.log('');

    console.log('🎉 所有测试完成！');
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

export { runTests };
