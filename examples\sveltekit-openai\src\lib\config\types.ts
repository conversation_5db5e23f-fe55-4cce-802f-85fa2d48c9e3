// src/lib/config/types.ts

export interface LanguageModelCapabilities {
  tools: boolean;
  images: boolean;
  parallel_tool_calls: boolean;
  prompt_cache_key: boolean;
}

export interface Model {
  name: string;
  display_name: string;
  max_tokens: number;
  capabilities: LanguageModelCapabilities;
  favorite?: boolean; // 新增字段，标记是否为收藏模型
}

export interface LanguageModel {
  name: string;
  display_name: string;
  max_tokens: number;
  capabilities: LanguageModelCapabilities;
}

export interface LanguageModelProvider {
  api_url: string;
  available_models: Model[];
}

export interface LanguageModelConfig {
  api_url: string;
  available_models: Model[];
}

export interface LanguageModelsConfig {
  openai: LanguageModelProvider;
  qwen?: LanguageModelProvider;
  zhipu?: LanguageModelProvider;
  kimi?: LanguageModelProvider;
}

export interface LLMConfig {
  language_models: LanguageModelsConfig;
}

export interface Config {
  language_models: LanguageModelsConfig;
}
