<script lang="ts">
  import ArrowUp from '$lib/components/icons/arrow-up.svelte';
  import Button from '$lib/components/ui/button/button.svelte';
  import { Textarea } from '$lib/components/ui/textarea/index.js';
  import { Chat } from '@ai-sdk/svelte';
  import { DefaultChatTransport } from 'ai';
  import StreamingMarkdown from '$lib/components/StreamingMarkdown.svelte';
  import ModelSelector from '$lib/components/ModelSelector.svelte';
  import { configManager } from '../lib/config/config-manager.js';
  import type { Model } from '../lib/config/types.js';

  const chat = new Chat({
    transport: new DefaultChatTransport({
      api: '/api/chat',
    }),
  });

  // 获取收藏的模型和所有模型
  const favoriteModels = configManager.getFavoriteModels();
  const allModelsGrouped: Record<string, Model[]> = configManager.getAllModelsGroupedByProvider();

  // 控制展开状态
  let isExpanded = $state(false);

  let input = $state('');

  const disabled = $derived(chat.status !== 'ready');

  function mapRoleToClass(role: string) {
    return role === 'assistant'
      ? 'chat-bubble-assistant'
      : 'bg-gray-50 text-gray-900 rounded-lg justify-self-end';
  }

  function handleSubmit(e: Event) {
    e.preventDefault();
    if (input.trim()) {
      chat.sendMessage({ text: input });
      input = '';
    }
  }

  // 预设的示例问题
  const sampleQuestions = [
    '请用 Markdown 格式介绍一下 Svelte 5 的新特性',
    '写一个关于 JavaScript 异步编程的教程，包含代码示例',
    '创建一个包含表格和列表的项目计划文档',
    '解释什么是响应式编程，并提供示例代码',
  ];

  function askSampleQuestion(question: string) {
    input = question;
    handleSubmit(new Event('submit'));
  }

  // 处理模型选择事件
  function handleModelSelect(event: CustomEvent<{ provider: string; model: Model }>) {
    const { provider, model } = event.detail;
    console.log('Selected model:', provider, model.name);
    // 这里可以添加实际的模型切换逻辑
  }
</script>

<main class="flex flex-col h-screen bg-gray-50">
  <!-- 头部 -->
  <header class="bg-white border-b border-gray-200 p-4">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-2xl font-bold text-gray-900">流式 Markdown AI 聊天</h1>
      <p class="text-gray-600 mt-1">
        体验 AI 回复的实时 Markdown 渲染和逐字淡入效果
      </p>
    </div>
  </header>

  <!-- 主要内容区域 -->
  <div class="flex-1 max-w-4xl mx-auto w-full flex flex-col">
    <!-- 消息区域 -->
    <div class="flex-1 overflow-y-auto p-4 space-y-4">
      {#if chat.messages.length === 0}
        <!-- 欢迎界面 -->
        <div class="text-center py-12">
          <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-700 mb-2">开始对话</h2>
            <p class="text-gray-500">
              AI 的回复将以流式 Markdown 格式呈现，支持逐字淡入动画
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto">
            {#each sampleQuestions as question (question)}
              <button
                onclick={() => askSampleQuestion(question)}
                class="p-3 text-left bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                {disabled}
              >
                <div class="text-sm text-gray-700">{question}</div>
              </button>
            {/each}
          </div>
        </div>
      {/if}

      {#each chat.messages as message (message.id)}
        <div
          class="flex {message.role === 'assistant'
            ? 'justify-start'
            : 'justify-end'}"
        >
          <div
            class="{mapRoleToClass(
              message.role,
            )} max-w-[100%] p-4 prose-container"
          >
            <!-- 角色标识 -->
            <div class="flex items-center mb-2">
              <div
                class="w-6 h-6 rounded-full {message.role === 'assistant'
                  ? 'bg-blue-500'
                  : 'bg-gray-500'} flex items-center justify-center text-white text-xs font-bold"
              >
                {message.role === 'assistant' ? 'AI' : 'U'}
              </div>
              <span
                class="ml-2 text-xs font-medium {message.role === 'assistant'
                  ? 'text-blue-700'
                  : 'text-gray-700'}"
              >
                {message.role === 'assistant' ? 'AI 助手' : '用户'}
              </span>
            </div>

            <!-- 消息内容 -->
            {#each message.parts as part, i (i)}
              {#if part.type === 'text'}
                {#if message.role === 'assistant'}
                  <div class="prose prose-sm max-w-none">
                    <StreamingMarkdown
                      content={part.text}
                      isStreaming={chat.status === 'streaming' &&
                        message === chat.lastMessage &&
                        (part.state === 'streaming' ||
                          part.state === undefined)}
                      animationDelay={15}
                      fadeInDuration={300}
                    />
                  </div>
                {:else}
                  <div class="whitespace-pre-wrap">{part.text}</div>
                {/if}
              {:else if part.type === 'tool-askForConfirmation'}
                {@const toolCallId = part.toolCallId}
                {@const state = part.state}

                {#if state === 'input-available'}
                  {@const input = part.input as { message: string }}
                  <div
                    class="flex flex-col gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded"
                  >
                    <div class="text-yellow-800">{input.message}</div>
                    <div class="flex gap-2">
                      <Button
                        variant="default"
                        size="sm"
                        onclick={() =>
                          chat.addToolResult({
                            toolCallId,
                            tool: 'askForConfirmation',
                            output: 'Yes, confirmed',
                          })}>确认</Button
                      >
                      <Button
                        variant="secondary"
                        size="sm"
                        onclick={() =>
                          chat.addToolResult({
                            toolCallId,
                            tool: 'askForConfirmation',
                            output: 'No, denied',
                          })}>取消</Button
                      >
                    </div>
                  </div>
                {:else if state === 'output-available'}
                  <div class="text-gray-600 text-sm">
                    {part.output}
                  </div>
                {/if}
              {:else if part.type === 'tool-getLocation'}
                {#if part.state === 'input-available'}
                  <div class="text-blue-600 text-sm">正在获取位置信息...</div>
                {:else if part.state === 'output-available'}
                  <div class="text-blue-600 text-sm">
                    位置: {part.output}
                  </div>
                {/if}
              {:else if part.type === 'tool-getWeatherInformation'}
                {#if part.state === 'input-available'}
                  {@const input = part.input as { city: string }}
                  <div class="text-green-600 text-sm">
                    正在获取 {input.city} 的天气信息...
                  </div>
                {:else if part.state === 'output-available'}
                  {@const input = part.input as { city: string }}
                  <div class="text-green-600 text-sm">
                    {input.city} 的天气: {part.output}
                  </div>
                {/if}
              {/if}
            {/each}
          </div>
        </div>
      {/each}

      <!-- 状态指示器 -->
      {#if chat.status === 'streaming' || chat.status === 'submitted'}
        <div class="flex justify-start">
          <div class="chat-bubble-assistant p-4 max-w-[100%] prose-container">
            <div class="flex items-center space-x-2">
              <div
                class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
              ></div>
              <div
                class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
                style="animation-delay: 0.1s"
              ></div>
              <div
                class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
                style="animation-delay: 0.2s"
              ></div>
              <span class="text-blue-700 text-sm ml-2">AI 正在思考...</span>
            </div>
          </div>
        </div>
      {/if}
    </div>

    <!-- 模型选择组件 -->
    <ModelSelector on:modelSelect={handleModelSelect} />

    <!-- 输入区域 -->
    <div class="border-t border-gray-200 bg-white p-4">
      <form class="relative max-w-4xl mx-auto" onsubmit={handleSubmit}>
        <Textarea
          bind:value={input}
          placeholder="输入您的问题... (支持 Shift+Enter 换行)"
          class="min-h-[60px] pr-12 resize-none"
          onkeydown={event => {
            if (event.key === 'Enter' && !event.shiftKey) {
              event.preventDefault();
              handleSubmit(event);
            }
          }}
        />
        <Button
          aria-label="发送消息"
          disabled={disabled || !input.trim()}
          type="submit"
          size="icon"
          class="absolute right-2 bottom-2"
        >
          <ArrowUp />
        </Button>
      </form>

      <div class="text-center mt-2">
        <span class="text-xs text-gray-500">
          状态: {chat.status === 'ready'
            ? '就绪'
            : chat.status === 'streaming'
              ? '处理中'
              : chat.status}
        </span>
      </div>
    </div>
  </div>
</main>

<style>
  /* 自定义滚动条样式 */
  :global(.overflow-y-auto::-webkit-scrollbar) {
    width: 6px;
  }

  :global(.overflow-y-auto::-webkit-scrollbar-track) {
    background: #f1f1f1;
    border-radius: 3px;
  }

  :global(.overflow-y-auto::-webkit-scrollbar-thumb) {
    background: #c1c1c1;
    border-radius: 3px;
  }

  :global(.overflow-y-auto::-webkit-scrollbar-thumb:hover) {
    background: #a8a8a8;
  }

  /* 确保 prose 样式不会影响动画 */
  :global(.prose) {
    max-width: none;
  }

  :global(.prose p) {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  :global(.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6) {
    margin-top: 1em;
    margin-bottom: 0.5em;
  }

  :global(
    .prose h1:first-child,
    .prose h2:first-child,
    .prose h3:first-child,
    .prose h4:first-child,
    .prose h5:first-child,
    .prose h6:first-child
  ) {
    margin-top: 0;
  }

  /* AI助手气泡框统一样式 */
  :global(.chat-bubble-assistant) {
    @apply bg-blue-50 text-blue-900 rounded-lg justify-self-start border border-blue-200;
  }

  /* 为包含prose内容的容器添加统一样式 */
  :global(.prose-container.chat-bubble-assistant) {
    @apply bg-blue-50 border border-blue-200;
  }
</style>
