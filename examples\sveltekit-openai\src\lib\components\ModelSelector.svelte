<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { configManager } from '../config/config-manager.js';
  import type { Model } from '../config/types.js';

  // 创建事件分发器
  const dispatch = createEventDispatcher();

  // 获取收藏的模型和所有模型
  const favoriteModels = configManager.getFavoriteModels();
  const allModelsGrouped: Record<string, Model[]> = configManager.getAllModelsGroupedByProvider();

  // 控制展开状态
  let isExpanded = $state(false);

  // 模型选择处理函数
  function handleModelSelect(provider: string, model: Model) {
    dispatch('modelSelect', { provider, model });
  }
</script>

<div class="border-t border-gray-200 bg-white p-2">
  <!-- 收藏模型横向列表 -->
  <div class="max-w-4xl mx-auto flex items-center gap-2 overflow-x-auto">
    {#each favoriteModels as favoriteModel (favoriteModel.provider + favoriteModel.model.name)}
      <button
        class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md whitespace-nowrap transition-colors"
        onclick={() => dispatch('modelSelect', { provider: favoriteModel.provider, model: favoriteModel.model })}
      >
        {favoriteModel.model.display_name}
      </button>
    {:else}
      <!-- 如果没有收藏模型，则显示展开按钮 -->
      <button
        class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md whitespace-nowrap transition-colors"
        onclick={() => isExpanded = !isExpanded}
      >
        展开 ▼
      </button>
    {/each}

    <!-- 展开按钮 -->
    <button
      class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md whitespace-nowrap transition-colors ml-auto"
      onclick={() => isExpanded = !isExpanded}
    >
      {isExpanded ? '收起 ▲' : '展开 ▼'}
    </button>
  </div>

  <!-- 展开的模型列表 -->
  {#if isExpanded}
    <div class="max-w-4xl mx-auto mt-2 p-2 bg-gray-50 rounded-md max-h-60 overflow-y-auto">
      {#each Object.entries(allModelsGrouped) as [provider, models] (provider)}
        <div class="mb-3 last:mb-0">
          <div class="font-bold text-gray-800 mb-1">{provider}</div>
          <div class="flex flex-wrap gap-2">
            {#each models as model (model.name)}
              <button
                class="px-2 py-1 text-xs bg-white hover:bg-gray-100 border border-gray-200 rounded-md transition-colors"
                onclick={() => dispatch('modelSelect', { provider, model })}
              >
                {model.display_name}
              </button>
            {/each}
          </div>
        </div>
      {/each}
    </div>
  {/if}
</div>
