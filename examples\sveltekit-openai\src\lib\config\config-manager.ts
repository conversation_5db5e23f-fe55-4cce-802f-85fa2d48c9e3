import type { LLMConfig, LanguageModelConfig, Model } from './types.js';
import defaultConfig from './default-config.json' with { type: 'json' };

// 配置管理器类
export class ConfigManager {
  private config: LLMConfig;

  constructor() {
    // 初始化时加载默认配置
    this.config = this.loadDefaultConfig();
  }

  // 加载默认配置
  private loadDefaultConfig(): LLMConfig {
    return defaultConfig as LLMConfig;
  }

  // 获取当前配置
  public getConfig(): LLMConfig {
    return this.config;
  }

  // 设置整个配置
  public setConfig(config: LLMConfig): void {
    this.config = config;
  }

  // 获取特定语言模型的配置
  public getModelConfig(provider: string): LanguageModelConfig | undefined {
    return (this.config.language_models as any)[provider];
  }

  // 设置特定语言模型的配置
  public setModelConfig(
    provider: string,
    modelConfig: LanguageModelConfig,
  ): void {
    (this.config.language_models as any)[provider] = modelConfig;
  }

  // 获取所有可用模型
  public getAllModels(): { provider: string; model: Model }[] {
    const models: { provider: string; model: Model }[] = [];

    for (const [provider, config] of Object.entries(
      this.config.language_models,
    )) {
      const typedConfig = config as LanguageModelConfig;
      typedConfig.available_models.forEach((model: Model) => {
        models.push({ provider, model });
      });
    }

    return models;
  }

  // 根据模型名称获取模型信息
  public getModelByName(
    provider: string,
    modelName: string,
  ): Model | undefined {
    const providerConfig = (this.config.language_models as any)[provider];
    if (!providerConfig) return undefined;

    return providerConfig.available_models.find(
      (model: Model) => model.name === modelName,
    );
  }

  // 更新模型配置
  public updateModel(
    provider: string,
    modelName: string,
    updates: Partial<Model>,
  ): boolean {
    const model = this.getModelByName(provider, modelName);
    if (!model) return false;

    Object.assign(model, updates);
    return true;
  }

  // 根据提供商获取模型信息
  public getModelsByProvider(
    provider: string,
  ): { provider: string; model: Model }[] {
    return this.getAllModels().filter(item => item.provider === provider);
  }

  // 获取收藏的模型
  public getFavoriteModels(): { provider: string; model: Model }[] {
    return this.getAllModels().filter(item => item.model.favorite === true);
  }

  // 获取按提供商分组的所有模型
  public getAllModelsGroupedByProvider(): Record<string, Model[]> {
    const grouped: Record<string, Model[]> = {};

    for (const [provider, config] of Object.entries(
      this.config.language_models,
    )) {
      grouped[provider] = config.available_models;
    }

    return grouped;
  }
}

// 创建全局配置管理器实例
export const configManager = new ConfigManager();

export default configManager;
