import type { LLMConfig, LanguageModelConfig, Model, DefaultModelRef } from './types.js';
import defaultConfig from './default-config.json' with { type: 'json' };

// 配置管理器类
export class ConfigManager {
  private config: LLMConfig;
  private currentModel?: DefaultModelRef; // 当前使用的模型
  private defaultMissing = false; // 标记默认模型是否缺失或无效

  constructor() {
    // 初始化时加载默认配置
    this.config = this.loadDefaultConfig();
    // 启动时检查默认模型是否存在
    this.ensureDefaultModel();
  }

  // 加载默认配置
  private loadDefaultConfig(): LLMConfig {
    return defaultConfig as LLMConfig;
  }

  // 确保默认模型存在；若不存在则选第一个可用模型
  private ensureDefaultModel(): void {
    const def = this.config.default_model;
    if (def && this.getModelByName(def.provider, def.name)) {
      this.currentModel = def;
      this.defaultMissing = false;
      return;
    }
    // 未配置或配置不存在：选择第一个模型
    const all = this.getAllModels();
    if (all.length > 0) {
      this.currentModel = { provider: all[0].provider, name: all[0].model.name };
      this.defaultMissing = true;
    } else {
      this.currentModel = undefined;
      this.defaultMissing = true;
    }
  }

  // 获取当前配置
  public getConfig(): LLMConfig {
    return this.config;
  }

  // 设置整个配置
  public setConfig(config: LLMConfig): void {
    this.config = config;
    this.ensureDefaultModel();
  }

  // 获取和设置默认模型
  public getDefaultModel(): DefaultModelRef | undefined {
    return this.config.default_model;
  }

  public setDefaultModel(ref: DefaultModelRef): void {
    this.config.default_model = ref;
    this.currentModel = ref;
  }

  // 当前模型（可与默认不同）
  public getCurrentModel(): DefaultModelRef | undefined {
    return this.currentModel ?? this.config.default_model;
  }

  public isDefaultMissing(): boolean {
    return this.defaultMissing;
  }

  public setCurrentModel(ref: DefaultModelRef): void {
    this.currentModel = ref;
  }

  // 获取特定语言模型的配置
  public getModelConfig(provider: string): LanguageModelConfig | undefined {
    return (this.config.language_models as any)[provider];
  }

  // 设置特定语言模型的配置
  public setModelConfig(
    provider: string,
    modelConfig: LanguageModelConfig,
  ): void {
    (this.config.language_models as any)[provider] = modelConfig;
  }

  // 获取所有可用模型
  public getAllModels(): { provider: string; model: Model }[] {
    const models: { provider: string; model: Model }[] = [];

    for (const [provider, config] of Object.entries(
      this.config.language_models,
    )) {
      const typedConfig = config as LanguageModelConfig;
      typedConfig.available_models.forEach((model: Model) => {
        models.push({ provider, model });
      });
    }

    return models;
  }

  // 根据模型名称获取模型信息
  public getModelByName(
    provider: string,
    modelName: string,
  ): Model | undefined {
    const providerConfig = (this.config.language_models as any)[provider];
    if (!providerConfig) return undefined;

    return providerConfig.available_models.find(
      (model: Model) => model.name === modelName,
    );
  }

  // 更新模型配置
  public updateModel(
    provider: string,
    modelName: string,
    updates: Partial<Model>,
  ): boolean {
    const model = this.getModelByName(provider, modelName);
    if (!model) return false;

    Object.assign(model, updates);
    return true;
  }

  // 根据提供商获取模型信息
  public getModelsByProvider(
    provider: string,
  ): { provider: string; model: Model }[] {
    return this.getAllModels().filter(item => item.provider === provider);
  }

  // 获取收藏的模型
  public getFavoriteModels(): { provider: string; model: Model }[] {
    return this.getAllModels().filter(item => item.model.favorite === true);
  }

  // 获取按提供商分组的所有模型
  public getAllModelsGroupedByProvider(): Record<string, Model[]> {
    const grouped: Record<string, Model[]> = {};

    for (const [provider, config] of Object.entries(
      this.config.language_models,
    )) {
      grouped[provider] = (config as LanguageModelConfig).available_models;
    }

    return grouped;
  }
}

// 创建全局配置管理器实例
export const configManager = new ConfigManager();

export default configManager;
