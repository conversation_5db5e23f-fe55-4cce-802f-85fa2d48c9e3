import{t as u,a as o,c as Q,b as ut}from"../chunks/CGanT4Ze.js";import{p as $t,t as I,a as jt,s as _,b as Z,g as t,d as Tt,c as s,u as S,r as e,f as J,n as ct}from"../chunks/Cc9lkQ6R.js";import{d as zt,e as Dt,s as h}from"../chunks/2Hh1nz_V.js";import{i as n}from"../chunks/0U5e1eCd.js";import{e as q,A as Et,i as ft}from"../chunks/zbNWLoGC.js";import{s as U}from"../chunks/krzcCheT.js";import{B as tt}from"../chunks/BrOQj_DL.js";import{T as Ft}from"../chunks/CEXehBSS.js";import{S as Pt}from"../chunks/CrxE95yh.js";import{C as Rt}from"../chunks/U6ZYfsmc.js";var Bt=(at,W,l)=>W(t(l)),Qt=u('<button class="p-3 text-left bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"><div class="text-sm text-gray-700"> </div></button>'),Jt=u('<div class="text-center py-12"><div class="mb-8"><h2 class="text-xl font-semibold text-gray-700 mb-2">开始对话</h2> <p class="text-gray-500">AI 的回复将以流式 Markdown 格式呈现，支持逐字淡入动画</p></div> <div class="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto"></div></div>'),Kt=u('<div class="prose prose-sm max-w-none"><!></div>'),Lt=u('<div class="whitespace-pre-wrap"> </div>'),Nt=u('<div class="flex flex-col gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded"><div class="text-yellow-800"> </div> <div class="flex gap-2"><!> <!></div></div>'),Ut=u('<div class="text-gray-600 text-sm"> </div>'),Wt=u('<div class="text-blue-600 text-sm">正在获取位置信息...</div>'),Yt=u('<div class="text-blue-600 text-sm"> </div>'),Gt=u('<div class="text-green-600 text-sm"> </div>'),Ht=u('<div class="text-green-600 text-sm"> </div>'),Ot=u('<div><div><div class="flex items-center mb-2"><div> </div> <span> </span></div> <!></div></div>'),Vt=u('<div class="flex justify-start"><div class="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-[85%]"><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div> <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div> <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div> <span class="text-blue-700 text-sm ml-2">AI 正在思考...</span></div></div></div>'),Xt=u('<main class="flex flex-col h-screen bg-gray-50"><header class="bg-white border-b border-gray-200 p-4"><div class="max-w-4xl mx-auto"><h1 class="text-2xl font-bold text-gray-900">流式 Markdown AI 聊天</h1> <p class="text-gray-600 mt-1">体验 AI 回复的实时 Markdown 渲染和逐字淡入效果</p></div></header> <div class="flex-1 max-w-4xl mx-auto w-full flex flex-col"><div class="flex-1 overflow-y-auto p-4 space-y-4"><!> <!> <!></div> <div class="border-t border-gray-200 bg-white p-4"><form class="relative max-w-4xl mx-auto"><!> <!></form> <div class="text-center mt-2"><span class="text-xs text-gray-500"> </span></div></div></div></main>');function va(at,W){$t(W,!0);const l=new Rt({api:"/api/chat"});let M=Tt("");const et=S(()=>l.status!=="ready");function mt(a){return a==="assistant"?"bg-blue-50 text-blue-900 rounded-lg border border-blue-200":"bg-gray-50 text-gray-900 rounded-lg border border-gray-200 justify-self-end"}function Y(a){a.preventDefault(),t(M).trim()&&(l.sendMessage({text:t(M)}),Z(M,""))}const xt=["请用 Markdown 格式介绍一下 Svelte 5 的新特性","写一个关于 JavaScript 异步编程的教程，包含代码示例","创建一个包含表格和列表的项目计划文档","解释什么是响应式编程，并提供示例代码"];function bt(a){Z(M,a,!0),Y(new Event("submit"))}var G=Xt(),st=_(s(G),2),H=s(st),rt=s(H);{var _t=a=>{var i=Jt(),$=_(s(i),2);q($,21,()=>xt,ft,(E,T)=>{var p=Qt();p.__click=[Bt,bt,T];var L=s(p),F=s(L,!0);e(L),e(p),I(()=>{p.disabled=t(et),h(F,t(T))}),o(E,p)}),e($),e(i),o(a,i)};n(rt,a=>{l.messages.length===0&&a(_t)})}var ot=_(rt,2);q(ot,17,()=>l.messages,a=>a.id,(a,i)=>{var $=Ot(),E=s($),T=s(E),p=s(T),L=s(p,!0);e(p);var F=_(p,2),kt=s(F,!0);e(F),e(T);var It=_(T,2);q(It,17,()=>t(i).parts,ft,(O,r)=>{var nt=Q(),Ct=J(nt);{var At=z=>{var N=Q(),V=J(N);{var X=b=>{var c=Kt(),A=s(c);const g=S(()=>l.status==="streaming"&&t(i)===l.lastMessage&&(t(r).state==="streaming"||t(r).state===void 0));Pt(A,{get content(){return t(r).text},get isStreaming(){return t(g)},animationDelay:15,fadeInDuration:300}),e(c),o(b,c)},C=b=>{var c=Lt(),A=s(c,!0);e(c),I(()=>h(A,t(r).text)),o(b,c)};n(V,b=>{t(i).role==="assistant"?b(X):b(C,!1)})}o(z,N)},St=(z,N)=>{{var V=C=>{var b=Q();const c=S(()=>t(r).toolCallId),A=S(()=>t(r).state);var g=J(b);{var D=f=>{var y=Nt();const m=S(()=>t(r).input);var d=s(y),w=s(d,!0);e(d);var v=_(d,2),x=s(v);tt(x,{variant:"default",size:"sm",onclick:()=>l.addToolResult({toolCallId:t(c),tool:"askForConfirmation",output:"Yes, confirmed"}),children:(k,R)=>{ct();var B=ut("确认");o(k,B)},$$slots:{default:!0}});var j=_(x,2);tt(j,{variant:"secondary",size:"sm",onclick:()=>l.addToolResult({toolCallId:t(c),tool:"askForConfirmation",output:"No, denied"}),children:(k,R)=>{ct();var B=ut("取消");o(k,B)},$$slots:{default:!0}}),e(v),e(y),I(()=>h(w,t(m).message)),o(f,y)},P=(f,y)=>{{var m=d=>{var w=Ut(),v=s(w,!0);e(w),I(()=>h(v,t(r).output)),o(d,w)};n(f,d=>{t(A)==="output-available"&&d(m)},y)}};n(g,f=>{t(A)==="input-available"?f(D):f(P,!1)})}o(C,b)},X=(C,b)=>{{var c=g=>{var D=Q(),P=J(D);{var f=m=>{var d=Wt();o(m,d)},y=(m,d)=>{{var w=v=>{var x=Yt(),j=s(x);e(x),I(()=>h(j,`位置: ${t(r).output??""}`)),o(v,x)};n(m,v=>{t(r).state==="output-available"&&v(w)},d)}};n(P,m=>{t(r).state==="input-available"?m(f):m(y,!1)})}o(g,D)},A=(g,D)=>{{var P=f=>{var y=Q(),m=J(y);{var d=v=>{var x=Gt();const j=S(()=>t(r).input);var k=s(x);e(x),I(()=>h(k,`正在获取 ${t(j).city??""} 的天气信息...`)),o(v,x)},w=(v,x)=>{{var j=k=>{var R=Ht();const B=S(()=>t(r).input);var Mt=s(R);e(R),I(()=>h(Mt,`${t(B).city??""} 的天气: ${t(r).output??""}`)),o(k,R)};n(v,k=>{t(r).state==="output-available"&&k(j)},x)}};n(m,v=>{t(r).state==="input-available"?v(d):v(w,!1)})}o(f,y)};n(g,f=>{t(r).type==="tool-getWeatherInformation"&&f(P)},D)}};n(C,g=>{t(r).type==="tool-getLocation"?g(c):g(A,!1)},b)}};n(z,C=>{t(r).type==="tool-askForConfirmation"?C(V):C(X,!1)},N)}};n(Ct,z=>{t(r).type==="text"?z(At):z(St,!1)})}o(O,nt)}),e(E),e($),I(O=>{U($,1,`flex ${t(i).role==="assistant"?"justify-start":"justify-end"}`),U(E,1,`${O??""} max-w-[85%] p-4`),U(p,1,`w-6 h-6 rounded-full ${t(i).role==="assistant"?"bg-blue-500":"bg-gray-500"} flex items-center justify-center text-white text-xs font-bold`),h(L,t(i).role==="assistant"?"AI":"U"),U(F,1,`ml-2 text-xs font-medium ${t(i).role==="assistant"?"text-blue-700":"text-gray-700"}`),h(kt,t(i).role==="assistant"?"AI 助手":"用户")},[()=>mt(t(i).role)]),o(a,$)});var pt=_(ot,2);{var gt=a=>{var i=Vt();o(a,i)};n(pt,a=>{(l.status==="streaming"||l.status==="submitted")&&a(gt)})}e(H);var it=_(H,2),K=s(it),lt=s(K);Ft(lt,{placeholder:"输入您的问题... (支持 Shift+Enter 换行)",class:"min-h-[60px] pr-12 resize-none",onkeydown:a=>{a.key==="Enter"&&!a.shiftKey&&(a.preventDefault(),Y(a))},get value(){return t(M)},set value(a){Z(M,a,!0)}});var yt=_(lt,2);const ht=S(()=>t(et)||!t(M).trim());tt(yt,{"aria-label":"发送消息",get disabled(){return t(ht)},type:"submit",size:"icon",class:"absolute right-2 bottom-2",children:(a,i)=>{Et(a,{})},$$slots:{default:!0}}),e(K);var vt=_(K,2),dt=s(vt),wt=s(dt);e(dt),e(vt),e(it),e(st),e(G),I(()=>h(wt,`状态: ${(l.status==="ready"?"就绪":l.status==="loading"?"处理中":l.status)??""}`)),Dt("submit",K,Y),o(at,G),jt()}zt(["click"]);export{va as component};
