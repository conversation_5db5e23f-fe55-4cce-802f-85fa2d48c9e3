<script lang="ts">
  import ModelSelectMenu from '$lib/components/menus/ModelSelectMenu.svelte';
  import { createEventDispatcher } from 'svelte';

  const dispatch = createEventDispatcher();
</script>

<div class="px-1 py-2">
  <div class="max-w-4xl mx-auto flex items-center gap-3">
    <!-- 第一个工具：选择模型 -->
    <ModelSelectMenu on:select={(e) => dispatch('modelSelect', e.detail)} />
    <!-- 预留其它工具按钮位 -->
  </div>
</div>

