import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types.js';
import { configManager } from '$lib/config/config-manager.js';

// GET /api/llm-config/models - 获取所有可用模型
export const GET: RequestHandler = async () => {
  try {
    const models = configManager.getAllModels();
    return json({
      success: true,
      data: models,
    });
  } catch (error) {
    console.error('获取模型列表时出错:', error);
    return json(
      {
        success: false,
        error: '获取模型列表失败',
      },
      { status: 500 },
    );
  }
};
